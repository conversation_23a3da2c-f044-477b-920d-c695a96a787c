/usr/bin/c++ -fPIC  -march=native -mtune=native -ffast-math -fopenmp -fno-math-errno -fno-trapping-math -g -shared -Wl,-soname,libSCR_5000_AI.so -o libSCR_5000_AI.so CMakeFiles/SCR_5000_AI.dir/src/KalmanFilter3D.cpp.o CMakeFiles/SCR_5000_AI.dir/src/PointTracker.cpp.o CMakeFiles/SCR_5000_AI.dir/src/config.cpp.o CMakeFiles/SCR_5000_AI.dir/src/fft_gpu.cpp.o CMakeFiles/SCR_5000_AI.dir/src/infer_engine.cpp.o CMakeFiles/SCR_5000_AI.dir/src/libSCR_5000_Alg.cpp.o CMakeFiles/SCR_5000_AI.dir/src/logger.cpp.o CMakeFiles/SCR_5000_AI.dir/src/postprocess.cpp.o CMakeFiles/SCR_5000_AI.dir/src/preprocess.cpp.o CMakeFiles/SCR_5000_AI.dir/src/utils.cpp.o  -Wl,-rpath,/usr/local/lib:/home/<USER>/My_APP/TensorRT-8.6.1.6/lib:/usr/local/cuda/lib64 /usr/local/lib/libopencv_gapi.so.4.8.1 /usr/local/lib/libopencv_highgui.so.4.8.1 /usr/local/lib/libopencv_ml.so.4.8.1 /usr/local/lib/libopencv_objdetect.so.4.8.1 /usr/local/lib/libopencv_photo.so.4.8.1 /usr/local/lib/libopencv_stitching.so.4.8.1 /usr/local/lib/libopencv_video.so.4.8.1 /usr/local/lib/libopencv_videoio.so.4.8.1 /usr/local/cuda/lib64/libcudart_static.a -ldl /usr/lib/x86_64-linux-gnu/librt.a /home/<USER>/My_APP/TensorRT-8.6.1.6/lib/libnvinfer.so /home/<USER>/My_APP/TensorRT-8.6.1.6/lib/libnvinfer_plugin.so /usr/local/cuda/lib64/libcufft.so /usr/local/lib/libopencv_imgcodecs.so.4.8.1 /usr/local/lib/libopencv_dnn.so.4.8.1 /usr/local/lib/libopencv_calib3d.so.4.8.1 /usr/local/lib/libopencv_features2d.so.4.8.1 /usr/local/lib/libopencv_flann.so.4.8.1 /usr/local/lib/libopencv_imgproc.so.4.8.1 /usr/local/lib/libopencv_core.so.4.8.1 /usr/lib/gcc/x86_64-linux-gnu/11/libgomp.so /usr/lib/x86_64-linux-gnu/libpthread.a 
