#include "libSCR_5000_Alg.hpp"
#include "preprocess.hpp"
#include "postprocess.hpp"
#include "infer_engine.hpp"
#include "PointTracker.hpp"
#include "logger.hpp"
#include "config.hpp"
#include "fft_gpu.hpp"

#include "TutorialConfig.h"

#include <cuda_runtime_api.h>
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <memory>
#include <ctime>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <stdexcept>

// ==================== 线程安全的全局资源管理 ====================
// 线程安全的初始化状态管理
static std::mutex g_init_mutex;
static std::atomic<bool> g_initialized{false};

// TensorRT资源 - 使用智能指针管理
struct TensorRTResources {
    std::unique_ptr<nvinfer1::IRuntime, void(*)(nvinfer1::IRuntime*)> runtime{nullptr, [](nvinfer1::IRuntime* p) { if(p) p->destroy(); }};
    std::unique_ptr<nvinfer1::ICudaEngine, void(*)(nvinfer1::ICudaEngine*)> engine{nullptr, [](nvinfer1::ICudaEngine* p) { if(p) p->destroy(); }};
    std::unique_ptr<nvinfer1::IExecutionContext, void(*)(nvinfer1::IExecutionContext*)> context{nullptr, [](nvinfer1::IExecutionContext* p) { if(p) p->destroy(); }};

    struct CudaStreamDeleter {
        void operator()(cudaStream_t* stream) {
            if (stream && *stream) {
                cudaStreamDestroy(*stream);
                delete stream;
            }
        }
    };
    std::unique_ptr<cudaStream_t, CudaStreamDeleter> stream{nullptr};

    struct CudaBufferDeleter {
        void operator()(void* ptr) {
            if (ptr) cudaFree(ptr);
        }
    };
    std::unique_ptr<void, CudaBufferDeleter> input_buffer{nullptr};
    std::unique_ptr<void, CudaBufferDeleter> output_buffer{nullptr};

    std::vector<float> output_prob;
    std::vector<float> sample_input;

    // 清理所有资源
    void cleanup() {
        output_buffer.reset();
        input_buffer.reset();
        stream.reset();
        context.reset();
        engine.reset();
        runtime.reset();
        output_prob.clear();
        output_prob.shrink_to_fit();
        sample_input.clear();
        sample_input.shrink_to_fit();
    }
};

static std::unique_ptr<TensorRTResources> g_tensorrt_resources = nullptr;

// 跟踪器资源
static std::unique_ptr<PointTracker> g_tracker = nullptr;
static std::vector<Point> g_current_group_detections;
static int g_group_start_frame = -1;
static const int FRAMES_PER_GROUP = 120;
static float g_prev_azimuth = -999.0f;
static bool g_azimuth_unchanged = false;

static std::unique_ptr<FFTGPUOptimizer> g_fft = nullptr;
static std::unique_ptr<ConfigManager> g_config_manager = nullptr;

// 内存监控类
class MemoryMonitor {
public:
    static void logMemoryUsage(const std::string& checkpoint) {
        try {
            size_t free_mem = 0, total_mem = 0;
            cudaError_t status = cudaMemGetInfo(&free_mem, &total_mem);
            if (status == cudaSuccess) {
                size_t used_mem = total_mem - free_mem;
                spdlog::info("GPU Memory at {}: Used {:.1f}MB, Free {:.1f}MB, Total {:.1f}MB",
                            checkpoint,
                            used_mem / (1024.0 * 1024.0),
                            free_mem / (1024.0 * 1024.0),
                            total_mem / (1024.0 * 1024.0));
            } else {
                spdlog::warn("Failed to get GPU memory info at {}: {}", checkpoint, cudaGetErrorString(status));
            }
        } catch (const std::exception& e) {
            spdlog::warn("Exception in memory monitoring at {}: {}", checkpoint, e.what());
        }
    }

    static void logCPUMemoryUsage(const std::string& checkpoint) {
        try {
            std::ifstream status_file("/proc/self/status");
            std::string line;
            while (std::getline(status_file, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label, value, unit;
                    iss >> label >> value >> unit;
                    spdlog::info("CPU Memory at {}: RSS {}KB", checkpoint, value);
                    break;
                }
            }
        } catch (const std::exception& e) {
            spdlog::warn("Exception in CPU memory monitoring at {}: {}", checkpoint, e.what());
        }
    }
};

// using BatchCb = std::function<void(const TrackingResult*, int /*batch_size*/)>;

// ==================== 内部辅助函数 ====================
ALGORITHM_API void GetVersionInfo(AlgorithmVersion* version_info) {
    static const AlgorithmVersion version = {
        ALGO_VERSION_MAJOR,
        ALGO_VERSION_MINOR,
        ALGO_VERSION_PATCH,
        ALGO_VERSION_STRING,
        ALGO_BUILD_TIMESTAMP
    };
    if (version_info) {
        *version_info = version;
    }
}

// 清理TensorRT资源的内部函数
static void CleanupTensorRTResources() {
    std::lock_guard<std::mutex> lock(g_init_mutex);
    if (g_tensorrt_resources) {
        g_tensorrt_resources->cleanup();
        g_tensorrt_resources.reset();
    }
    g_initialized.store(false);
}

// 初始化TensorRT引擎
static int InitializeTensorRT(const std::string& engine_path) {
    std::lock_guard<std::mutex> lock(g_init_mutex);

    if (g_initialized.load()) {
        spdlog::debug("TensorRT already initialized");
        return 0;
    }

    try {
        spdlog::info("初始化TensorRT引擎: {}", engine_path);

        // 创建TensorRT资源管理器
        g_tensorrt_resources = std::make_unique<TensorRTResources>();

        // 初始化自定义插件
        initializeCustomPlugins();

        // 加载引擎
        nvinfer1::IRuntime* runtime_ptr = nullptr;
        nvinfer1::ICudaEngine* engine_ptr = loadEngine(engine_path, runtime_ptr);
        if (!engine_ptr) {
            spdlog::error("Failed to load TensorRT engine from: {}", engine_path);
            return -1;
        }

        // 使用智能指针管理资源
        g_tensorrt_resources->runtime.reset(runtime_ptr);
        g_tensorrt_resources->engine.reset(engine_ptr);

        // 创建执行上下文
        nvinfer1::IExecutionContext* context_ptr = engine_ptr->createExecutionContext();
        if (!context_ptr) {
            spdlog::error("Failed to create execution context");
            CleanupTensorRTResources();
            return -1;
        }
        g_tensorrt_resources->context.reset(context_ptr);

        // 创建CUDA流
        cudaStream_t* stream_ptr = new cudaStream_t;
        cudaError_t cuda_status = cudaStreamCreate(stream_ptr);
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to create CUDA stream: {}", cudaGetErrorString(cuda_status));
            delete stream_ptr;
            CleanupTensorRTResources();
            return -1;
        }
        g_tensorrt_resources->stream.reset(stream_ptr);

        // 获取输入输出维度
        const auto input_dims = engine_ptr->getBindingDimensions(0);
        const auto output_dims = engine_ptr->getBindingDimensions(1);

        const int input_h = input_dims.d[2];
        const int input_w = input_dims.d[3];
        const size_t input_size = input_dims.d[4] * input_h * input_w;
        const size_t output_size = output_dims.d[2] * output_dims.d[3];

        spdlog::info("模型输入维度: [{}x{}x{}x{}]", input_dims.d[1], input_dims.d[2], input_dims.d[3], input_dims.d[4]);

        // 分配GPU内存
        void* input_buffer_ptr = nullptr;
        cuda_status = cudaMalloc(&input_buffer_ptr, input_size * sizeof(float));
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to allocate input buffer: {}", cudaGetErrorString(cuda_status));
            CleanupTensorRTResources();
            return -1;
        }
        g_tensorrt_resources->input_buffer.reset(input_buffer_ptr);

        void* output_buffer_ptr = nullptr;
        cuda_status = cudaMalloc(&output_buffer_ptr, output_size * sizeof(float));
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to allocate output buffer: {}", cudaGetErrorString(cuda_status));
            CleanupTensorRTResources();
            return -1;
        }
        g_tensorrt_resources->output_buffer.reset(output_buffer_ptr);

        // 分配CPU内存
        try {
            g_tensorrt_resources->output_prob.resize(output_size);
            g_tensorrt_resources->sample_input.resize(1024 * 1024 * 2);
        } catch (const std::bad_alloc& e) {
            spdlog::error("Failed to allocate CPU memory: {}", e.what());
            CleanupTensorRTResources();
            return -1;
        }

        g_initialized.store(true);
        spdlog::info("TensorRT引擎初始化成功");

        // 记录初始化后的内存使用情况
        MemoryMonitor::logMemoryUsage("TensorRT initialization complete");
        MemoryMonitor::logCPUMemoryUsage("TensorRT initialization complete");

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception during TensorRT initialization: {}", e.what());
        CleanupTensorRTResources();
        return -1;
    }
}

// ==================== 公共API函数 ====================

// 算法库初始化
ALGORITHM_API int InitializeAlgorithmLibrary(const char* config_path) {
    if (!config_path) {
        spdlog::error("Invalid config path");
        return -1;
    }
    // 加载配置
    g_config_manager = std::make_unique<ConfigManager>(config_path);

    // 日志路径
    std::string log_path = *g_config_manager->get<std::string>("/io_settings/logging/log_file_path");

    // 初始化日志
    initLogger(log_path);

    // 开启日志
    spdlog::info("=====算法库初始化开始...=====");

    // 初始化TensorRT引擎（如果尚未初始化）
    if (!g_initialized.load()) {

        std::string engine_path = *g_config_manager->get<std::string>("/io_settings/engine_paths");

        int init_result = -1;
        std::ifstream test_file(engine_path);
        if (test_file.good()) {
            init_result = InitializeTensorRT(engine_path);
        }

        if (init_result != 0) {
            spdlog::error("Failed to initialize TensorRT engine");
            return -2;
        }
    }

    // 加载查表数据
    std::string table_path = *g_config_manager->get<std::string>("/io_settings/table_paths");

    bool table_loaded = false;
    std::ifstream test_file(table_path);
    if (test_file.good()) {
        spdlog::info("加载俯仰角查表数据: {}", table_path);
        loadHechaTable_(table_path);
        table_loaded = true;
    }

    if (!table_loaded) {
        spdlog::warn("Could not find hecha_table.csv, using default values");
    }

    // 初始化跟踪器(json配置文件修改)
    g_tracker = std::make_unique<PointTracker>(
        *g_config_manager->get<int>("/algorithm_settings/tracker/max_age"),
        *g_config_manager->get<int>("/algorithm_settings/tracker/reid_age"),
        *g_config_manager->get<float>("/algorithm_settings/tracker/distance_threshold"),
        *g_config_manager->get<int>("/algorithm_settings/tracker/min_hits")
    );
    spdlog::info("跟踪器初始化成功: max_age={}, reid_age={}, distance_threshold={}, min_hits={}",
        *g_config_manager->get<int>("/algorithm_settings/tracker/max_age"),
        *g_config_manager->get<int>("/algorithm_settings/tracker/reid_age"),
        *g_config_manager->get<float>("/algorithm_settings/tracker/distance_threshold"),
        *g_config_manager->get<int>("/algorithm_settings/tracker/min_hits")
    );

    // 初始化GPU FFT优化器
    g_fft = std::make_unique<FFTGPUOptimizer>(1024, 2048);

    return 0;
}

// ==================== 目标检测算法实现 ====================
ALGORITHM_API int TargetDetection(
    char* input_head_data,
    char* input_data,
    DetectionResult** detection_results,
    int* num_detections,
    const FrameHeader_Alg** S_head,
    ColumnSegmentData** column_segments,
    int* num_segments
) {
    spdlog::info("=====开始执行轻量化目标检测...=====");

    // 参数验证
    if (!input_data || !detection_results || !num_detections || !S_head || !column_segments || !num_segments) {
        spdlog::error("Invalid input parameters for TargetDetection");
        return -1;
    }

    // size_t frame_idx = 1;
    auto frame_view = getFrameDataView(input_head_data, input_data, *g_config_manager->get<int>("/algorithm_settings/detection/frame_idx"));
    const FrameHeader_Alg* S_head_ptr = frame_view.head_S;
    const float* S_data = frame_view.data_S;
    const FrameHeader_Alg* D_head = frame_view.head_D;
    const float* D_data = frame_view.data_D;

    // 初始化输出参数
    *detection_results = nullptr;
    *num_detections = 0;
    *S_head = S_head_ptr;
    *column_segments = nullptr;
    *num_segments = 0;

    try {

        // 检查TensorRT资源是否已初始化
        if (!g_tensorrt_resources) {
            spdlog::error("TensorRT resources not initialized");
            return -1;
        }

        // 预处理：采样和归一化
        auto t0 = std::chrono::high_resolution_clock::now();
        sample_and_normalize(S_data, 1024 * 2048 * 2, g_tensorrt_resources->sample_input);
        auto t1 = std::chrono::high_resolution_clock::now();

        // 上传数据到GPU
        cudaError_t cuda_status = cudaMemcpyAsync(
            g_tensorrt_resources->input_buffer.get(),
            g_tensorrt_resources->sample_input.data(),
            g_tensorrt_resources->sample_input.size() * sizeof(float),
            cudaMemcpyHostToDevice,
            *g_tensorrt_resources->stream
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy input data to GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        // 执行推理
        void* buffers[2] = {g_tensorrt_resources->input_buffer.get(), g_tensorrt_resources->output_buffer.get()};
        bool inference_result = g_tensorrt_resources->context->enqueueV2(buffers, *g_tensorrt_resources->stream, nullptr);
        if (!inference_result) {
            spdlog::error("TensorRT inference failed");
            return -1;
        }

        // 下载结果
        cuda_status = cudaMemcpyAsync(
            g_tensorrt_resources->output_prob.data(),
            g_tensorrt_resources->output_buffer.get(),
            g_tensorrt_resources->output_prob.size() * sizeof(float),
            cudaMemcpyDeviceToHost,
            *g_tensorrt_resources->stream
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy output data from GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        cudaStreamSynchronize(*g_tensorrt_resources->stream);
        auto t2 = std::chrono::high_resolution_clock::now();

        // 基本后处理：二值化和连通组件分析
        auto centers = post_process_combined(g_tensorrt_resources->output_prob.data(), 512, 1024,
            *g_config_manager->get<int>("/algorithm_settings/detection/threshold"),
            *g_config_manager->get<float>("/algorithm_settings/detection/max_ratio"),
            *g_config_manager->get<int>("/algorithm_settings/detection/min_area"));

        if (centers.empty()) {
            spdlog::warn("无效帧:{} 模型没有检测到目标，跳过处理", S_head_ptr[0].frame_num);
            *detection_results = nullptr;
            *num_detections = 0;
            return 0;
        }

        if (centers[0].first == -1) {
            spdlog::warn("异常帧:{} 目标过多，跳过处理", S_head_ptr[0].frame_num);
            *detection_results = nullptr;
            *num_detections = 0;
            return 0;
        }

        auto t3 = std::chrono::high_resolution_clock::now();

        // 分配输出内存
        *num_detections = centers.size();
        if (*num_detections > 0) {
            try {
                *detection_results = new DetectionResult[*num_detections];
            } catch (const std::bad_alloc& e) {
                spdlog::error("Failed to allocate memory for detection results: {}", e.what());
                *num_detections = 0;
                return -1;
            }
        } else {
            *detection_results = nullptr;
        }

        spdlog::info("检测到 {} 个目标中心点", *num_detections);

        // 填充轻量化检测结果 - 仅包含基本信息
        for (size_t i = 0; i < centers.size(); ++i) {
            (*detection_results)[i].row = centers[i].second;  // y坐标对应行
            (*detection_results)[i].col = centers[i].first;   // x坐标对应列
            (*detection_results)[i].frame = S_head_ptr[0].frame_num; // 从帧头获取帧号

            spdlog::info("检测点[{}]: 行={}, 列={}, 帧={}", i,
                         (*detection_results)[i].row, (*detection_results)[i].col, (*detection_results)[i].frame);
        }

        // 提取列数据段供跟踪函数使用
        spdlog::debug("开始提取列数据段，共 {} 个中心点", centers.size());
        *num_segments = centers.size();
        try {
            *column_segments = new ColumnSegmentData[*num_segments];
        } catch (const std::bad_alloc& e) {
            spdlog::error("Failed to allocate memory for column segments: {}", e.what());
            // 清理已分配的检测结果内存
            if (*detection_results) {
                delete[] *detection_results;
                *detection_results = nullptr;
            }
            *num_detections = 0;
            *num_segments = 0;
            return -1;
        }

        for (size_t i = 0; i < centers.size(); ++i) {
            int col = centers[i].first;
            int row = centers[i].second;
            try {
                (*column_segments)[i] = extractColumnSegmentData(S_data, D_data, col, row);
                spdlog::debug("提取列数据段[{}]: 列={}, 行={}, 段长度={}", i, col, row, (*column_segments)[i].segment_length);
            } catch (const std::exception& e) {
                spdlog::error("Failed to extract column segment for center {}: {}", i, e.what());
                // 清理已分配的内存
                for (size_t j = 0; j < i; ++j) {
                    delete[] (*column_segments)[j].data_S;
                    delete[] (*column_segments)[j].data_D;
                }
                delete[] *column_segments;
                *column_segments = nullptr;
                if (*detection_results) {
                    delete[] *detection_results;
                    *detection_results = nullptr;
                }
                *num_detections = 0;
                *num_segments = 0;
                return -1;
            }
        }

        auto t4 = std::chrono::high_resolution_clock::now();

        // 记录性能信息
        int t_pre = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        int t_inf = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
        int t_post = std::chrono::duration_cast<std::chrono::milliseconds>(t3 - t2).count();
        int t_extract = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t3).count();
        int total = t_pre + t_inf + t_post + t_extract;

        spdlog::info("目标检测耗时(ms): 预处理:{} 推理:{} 后处理:{} 提取:{} 总:{} (FPS:{:.2f})",
                     t_pre, t_inf, t_post, t_extract, total, 1000.0 / total);

        // 记录目标检测后的内存使用情况
        MemoryMonitor::logMemoryUsage("Target detection complete");

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetDetection: {}", e.what());
        // 清理已分配的内存
        if (*detection_results) {
            delete[] *detection_results;
            *detection_results = nullptr;
        }
        *num_detections = 0;
        return -1;
    }
}

// ==================== 目标跟踪算法实现 ====================
// 功能：接收轻量化检测结果，执行FFT处理、俯仰角计算和目标跟踪
// 输入：检测结果中心点（使用缓存的雷达数据）
// 输出：完整的跟踪结果（包含位置、速度、雷达参数等）
ALGORITHM_API void TargetTracking(
    const DetectionResult* detection_results,
    int num_detections,
    const FrameHeader_Alg* S_head,
    const ColumnSegmentData* column_segments,
    int num_segments,
    TrackingResult** tracking_results,
    int* num_tracks,
    BatchCb batch_cb
) {
    spdlog::info("=====开始目标跟踪（包含FFT和俯仰角计算）...======");

    if (num_detections <= 0) {
        spdlog::warn("没有检测到目标，跳过跟踪");
        *tracking_results = nullptr;
        *num_tracks = 0;
        return;
    }

    // 参数验证
    if (!detection_results || !tracking_results || !num_tracks || !S_head || !column_segments || num_segments != num_detections) {
        spdlog::error("Invalid input parameters for TargetTracking");
        *tracking_results = nullptr;
        *num_tracks = 0;
        return;
    }

    // 确保跟踪器已初始化
    if (!g_tracker) {
        g_tracker = std::make_unique<PointTracker>(3, 20, 50.0f, 2);
        spdlog::info("跟踪器使用默认参数：max_age=3, reid_age=20, distance_threshold=50.0f, min_hits=2");
    }

    try {
        // 执行FFT处理和俯仰角计算
        auto t_fft_start = std::chrono::high_resolution_clock::now();

        // 将检测结果转换为中心点格式
        std::vector<std::pair<int, int>> centers;
        centers.reserve(num_detections);
        for (int i = 0; i < num_detections; ++i) {
            centers.emplace_back(detection_results[i].col, detection_results[i].row);
            spdlog::debug("处理检测点[{}]: 列={}, 行={}", i, detection_results[i].col, detection_results[i].row);
        }

        // 确保FFT优化器已初始化
        if (!g_fft) {
            spdlog::error("FFT优化器未初始化");
            *tracking_results = nullptr;
            *num_tracks = 0;
            return;
        }

        // 使用传入的列数据段进行FFT处理
        spdlog::debug("开始FFT处理，处理 {} 个列数据段", num_segments);
        std::vector<std::complex<float>> S_complexData;
        std::vector<std::complex<float>> D_complexData;
        S_complexData.reserve(num_segments);
        D_complexData.reserve(num_segments);

        for (int i = 0; i < num_segments; ++i) {
            const auto& segment = column_segments[i];

            // 对S通道和D通道数据分别执行FFT
            try {
                // 将S通道数据转换为cufftComplex格式
                std::vector<cufftComplex> S_segment_data(segment.segment_length);
                for (int j = 0; j < segment.segment_length; ++j) {
                    S_segment_data[j].x = segment.data_S[j * 2];     // 实部
                    S_segment_data[j].y = segment.data_S[j * 2 + 1]; // 虚部
                }

                // 将D通道数据转换为cufftComplex格式
                std::vector<cufftComplex> D_segment_data(segment.segment_length);
                for (int j = 0; j < segment.segment_length; ++j) {
                    D_segment_data[j].x = segment.data_D[j * 2];     // 实部
                    D_segment_data[j].y = segment.data_D[j * 2 + 1]; // 虚部
                }

                // 执行FFT处理
                auto S_result = g_fft->performFFTOnSegment(S_segment_data, segment.row, segment.segment_start);
                auto D_result = g_fft->performFFTOnSegment(D_segment_data, segment.row, segment.segment_start);

                S_complexData.push_back(S_result);
                D_complexData.push_back(D_result);

                spdlog::debug("FFT处理完成[{}]: 列={}, 行={}, 段长度={}", i, segment.col, segment.row, segment.segment_length);
            } catch (const std::exception& e) {
                spdlog::error("FFT处理失败[{}]: {}", i, e.what());
                // 使用原始数据作为备用
                int local_row = segment.row - segment.segment_start;
                if (local_row >= 0 && local_row < segment.segment_length) {
                    S_complexData.emplace_back(segment.data_S[local_row * 2], segment.data_S[local_row * 2 + 1]);
                    D_complexData.emplace_back(segment.data_D[local_row * 2], segment.data_D[local_row * 2 + 1]);
                } else {
                    S_complexData.emplace_back(0.0f, 0.0f);
                    D_complexData.emplace_back(0.0f, 0.0f);
                }
            }
        }

        auto t_fft_end = std::chrono::high_resolution_clock::now();

        // 计算俯仰角、方位角、距离等信息
        spdlog::debug("开始俯仰角计算");
        auto results = computeElevationAngles_GPU(S_head, S_complexData, D_complexData, centers, 1024, 2048);
        auto t_compute_end = std::chrono::high_resolution_clock::now();

        if (results.empty()) {
            spdlog::warn("俯仰角计算没有结果，跳过跟踪");
            *tracking_results = nullptr;
            *num_tracks = 0;
            return;
        }

        spdlog::info("FFT和俯仰角计算完成，得到 {} 个结果", results.size());

        // 将计算结果转换为Point格式
        std::vector<Point> current_detections;
        current_detections.reserve(results.size());

        for (const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] : results) {
            Point p;
            p.position[0] = x;
            p.position[1] = y;
            p.position[2] = z;
            p.velocity[0] = vx;
            p.velocity[1] = vy;
            p.velocity[2] = vz;
            p.type = 1; // 默认类型
            p.frame = frame;
            p.label = -1; // 初始化标签
            current_detections.push_back(p);
        }

        // 记录FFT和计算性能
        int t_fft = std::chrono::duration_cast<std::chrono::milliseconds>(t_fft_end - t_fft_start).count();
        int t_compute = std::chrono::duration_cast<std::chrono::milliseconds>(t_compute_end - t_fft_end).count();
        spdlog::info("FFT处理耗时: {}ms, 俯仰角计算耗时: {}ms", t_fft, t_compute);

        // 检查是否需要处理分组逻辑
        int current_frame = detection_results[0].frame;
        float current_azimuth = S_head[0].angle / 100.0f; // 从帧头获取方位角

        // 检查方位角是否变化
        g_azimuth_unchanged = (g_prev_azimuth != -999.0f) &&
                              (std::abs(current_azimuth - g_prev_azimuth) < 1.0f);
        g_prev_azimuth = current_azimuth;

        // 添加到当前组
        for (const auto& detection : current_detections) {
            g_current_group_detections.push_back(detection);
        }

        if (g_group_start_frame == -1) {
            g_group_start_frame = current_frame;
        }

        // 判断是否需要处理当前组
        bool should_process_group = false;
        if (g_azimuth_unchanged) {
            should_process_group = true;
            spdlog::info("云台未转动");
        } else {
            int frame_diff = current_frame - g_group_start_frame;
            if (frame_diff < 0) frame_diff += 65536; // 处理帧号回绕
            if (frame_diff >= FRAMES_PER_GROUP - 1) {
                should_process_group = true;
            }
        }

        std::vector<TrackResult> tracks;

        if (should_process_group && !g_current_group_detections.empty()) {
            spdlog::info("目标数量:{}, 起始帧:{}, 结束帧:{}",
                        g_current_group_detections.size(), g_group_start_frame, current_frame);

            // 聚类检测结果
            auto clustered = clusterDetections_DBSCAN(g_current_group_detections, *g_config_manager->get<float>("/algorithm_settings/cluster/eps"),
                                                    *g_config_manager->get<int>("/algorithm_settings/cluster/min_pts"),
                                                    *g_config_manager->get<int>("/algorithm_settings/cluster/num_threads"));
            // spdlog::info("聚类后数量: {}", clustered.size());

            // 执行跟踪
            tracks = g_tracker->update(clustered);
            // auto tracked_info = convertTracksToTargetInfos(tracks);

            spdlog::info("跟踪结果数量: {}", tracks.size());

            // 轨迹插值
            auto interpolated_tracks = g_tracker->interpolateTracks_seg(1.0f);
            for (const auto& step : interpolated_tracks) {
                // auto step_info = convertTracksToTargetInfos(step);
                tracks.insert(tracks.end(), step.begin(), step.end());
            }

            // 清理当前组
            g_current_group_detections.clear();
            g_group_start_frame = g_azimuth_unchanged ? -1 : current_frame + 1;
        } else {
            *tracking_results = nullptr;
            *num_tracks = 0;
            return;
        }

        // 转换跟踪结果
        if (tracks.empty()) {
            *tracking_results = nullptr;
            *num_tracks = 0;
            return;
        }

        // 分配输出内存
        *num_tracks = tracks.size();
        if (*num_tracks > 0) {
            try {
                *tracking_results = new TrackingResult[*num_tracks];
            } catch (const std::bad_alloc& e) {
                spdlog::error("Failed to allocate memory for tracking results: {}", e.what());
                *num_tracks = 0;
                return;
            }
        } else {
            *tracking_results = nullptr;
        }

        // 填充跟踪结果
        for (size_t i = 0; i < tracks.size(); ++i) {
            const auto& track = tracks[i];

            (*tracking_results)[i].id = track.id;
            (*tracking_results)[i].x = track.position[0];
            (*tracking_results)[i].y = track.position[1];
            (*tracking_results)[i].z = track.position[2];
            (*tracking_results)[i].vx = track.velocity[0];
            (*tracking_results)[i].vy = track.velocity[1];
            (*tracking_results)[i].vz = track.velocity[2];

            // 计算转换值
            float range = std::sqrt(track.position[0]*track.position[0] +
                                    track.position[1]*track.position[1] +
                                    track.position[2]*track.position[2]);
            (*tracking_results)[i].fMR = range;
            (*tracking_results)[i].fMV = (track.position[0]*track.velocity[0] +
                                            track.position[1]*track.velocity[1] +
                                            track.position[2]*track.velocity[2]) / (range + 1e-6f);
            (*tracking_results)[i].fMA = std::fmod(std::atan2(track.position[1], track.position[0]) * 180.0f / M_PI + 360.0f, 360.0f);
            (*tracking_results)[i].fME = std::atan2(track.position[2], std::sqrt(track.position[0]*track.position[0] + track.position[1]*track.position[1])) * 180.0f / M_PI;
        

            // 设置默认值
            (*tracking_results)[i].fSNR = 1.0f;
            (*tracking_results)[i].fEn = 1.0f;
            (*tracking_results)[i].fRcs = 1.0f;
            (*tracking_results)[i].type = 1;
            (*tracking_results)[i].FPGATimeLog = 1;
            (*tracking_results)[i].PreShow = 2;

            spdlog::info("Track[{:>2}] ID: {:>2} | Pos: ({:>7.2f}, {:>7.2f}, {:>7.2f}) m | Vel: ({:>6.2f}, {:>6.2f}, {:>6.2f}) m/s | R: {:>6.2f} m | Vr: {:>6.2f} m/s | Az: {:>6.2f}° | El: {:>6.2f}°",
                i,
                (*tracking_results)[i].id,
                (*tracking_results)[i].x, (*tracking_results)[i].y, (*tracking_results)[i].z,
                (*tracking_results)[i].vx, (*tracking_results)[i].vy, (*tracking_results)[i].vz,
                (*tracking_results)[i].fMR, (*tracking_results)[i].fMV,
                (*tracking_results)[i].fMA, (*tracking_results)[i].fME);
        }

        // 记录总体性能信息
        auto t_total_end = std::chrono::high_resolution_clock::now();
        int t_total = std::chrono::duration_cast<std::chrono::milliseconds>(t_total_end - t_fft_start).count();

        spdlog::info("跟踪完成，输出 {} 个轨迹，总耗时: {}ms (FFT: {}ms, 计算: {}ms)",
                     *num_tracks, t_total, t_fft, t_compute);

        if (batch_cb && *num_tracks > 0) {
            constexpr int kBatchSize = 6;
            int num_batches = *num_tracks / kBatchSize;
            for (int i = 0; i < kBatchSize; ++i) {
                int start = i * num_batches;
                int end = start + num_batches;
                batch_cb(*tracking_results + start, end - start);
            }
        }

        return;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetTracking: {}", e.what());
        // 清理已分配的内存
        if (*tracking_results) {
            delete[] *tracking_results;
            *tracking_results = nullptr;
        }
        *num_tracks = 0;
        return;
    }
}

// 释放检测结果内存
ALGORITHM_API void ReleaseDetectionResults(DetectionResult* detection_results) {
    if (detection_results) {
        delete[] detection_results;
        spdlog::debug("Detection results memory released");
    }
}

// 释放跟踪结果内存
ALGORITHM_API void ReleaseTrackingResults(TrackingResult* tracking_results) {
    if (tracking_results) {
        delete[] tracking_results;
        spdlog::debug("Tracking results memory released");
    }
}

// 释放所有资源
ALGORITHM_API void ReleaseAllResources() {
    try {
        spdlog::info("Resource cleanup started...");

        // 释放TensorRT和CUDA资源
        CleanupTensorRTResources();

        // 重置CUDA设备以确保所有资源被释放
        cudaError_t cuda_status = cudaDeviceReset();
        if (cuda_status != cudaSuccess) {
            spdlog::warn("CUDA device reset failed: {}", cudaGetErrorString(cuda_status));
        } else {
            spdlog::info("CUDA device reset successfully");
        }

        // 重置跟踪器和相关状态
        g_tracker.reset();
        g_current_group_detections.clear();
        g_current_group_detections.shrink_to_fit();
        g_group_start_frame = -1;
        g_prev_azimuth = -999.0f;
        g_azimuth_unchanged = false;

        // 重置初始化标志在CleanupTensorRTResources中已处理

        spdlog::info("All resources released successfully...");

    } catch (const std::exception& e) {
        spdlog::error("Exception during resource cleanup: {}", e.what());
    }
}